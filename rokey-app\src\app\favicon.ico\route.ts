import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET(request: NextRequest) {
  console.log('🔍 FAVICON REQUEST: /favicon.ico accessed');
  
  try {
    const logoPath = path.join(process.cwd(), 'public', 'RouKey_Logo_GLOW.png');
    console.log('🔍 FAVICON: Looking for logo at:', logoPath);
    
    // Check if file exists
    if (!fs.existsSync(logoPath)) {
      console.error('❌ FAVICON: Logo file not found at:', logoPath);
      return new NextResponse('Logo file not found', { status: 404 });
    }
    
    const logoBuffer = fs.readFileSync(logoPath);
    console.log('✅ FAVICON: Logo loaded successfully, size:', logoBuffer.length, 'bytes');
    
    return new NextResponse(logoBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'image/png',
        'Cache-Control': 'public, max-age=31536000, immutable',
      },
    });
  } catch (error) {
    console.error('❌ FAVICON ERROR:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return new NextResponse('Favicon error: ' + errorMessage, { status: 500 });
  }
}
