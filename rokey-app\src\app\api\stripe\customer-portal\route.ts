import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { createClient } from '@supabase/supabase-js';
import { createAbsoluteUrl } from '@/lib/utils/url';
import { STRIPE_KEYS } from '@/lib/stripe-config';

console.log('🔧 Stripe configuration:', {
  environment: process.env.NODE_ENV,
  secretKeyPrefix: STRIPE_KEYS.secretKey?.substring(0, 15) + '...',
  hasSecretKey: !!STRIPE_KEYS.secretKey
});

const stripe = new Stripe(STRIPE_KEYS.secretKey, {
  apiVersion: '2025-02-24.acacia',
});

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { userId, returnUrl } = body;

    console.log('🔄 Customer portal request received');
    console.log('👤 User ID:', userId);
    console.log('🔗 Return URL:', returnUrl);
    console.log('🌍 Environment:', process.env.NODE_ENV);

    if (!userId) {
      console.error('❌ Missing userId in request');
      return NextResponse.json(
        { error: 'Missing userId' },
        { status: 400 }
      );
    }

    // Get user's subscription record to find their Stripe customer ID
    // We look for any subscription record (active, canceled, etc.) since we need the customer ID
    const { data: subscription, error: subscriptionError } = await supabase
      .from('subscriptions')
      .select('stripe_customer_id')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    let stripeCustomerId: string | null = null;

    if (subscription && subscription.stripe_customer_id) {
      stripeCustomerId = subscription.stripe_customer_id;
    } else {
      // Fallback: Try to find customer by email in Stripe
      console.log('No subscription record found, trying to find customer by email...');

      // Get user email from auth
      const { data: userData, error: userError } = await supabase.auth.admin.getUserById(userId);

      if (userError || !userData.user?.email) {
        console.error('Could not get user email:', userError);
        return NextResponse.json(
          { error: 'No Stripe customer found for user' },
          { status: 404 }
        );
      }

      // Search for existing Stripe customer by email
      try {
        const existingCustomers = await stripe.customers.list({
          email: userData.user.email,
          limit: 1,
        });

        if (existingCustomers.data.length > 0) {
          stripeCustomerId = existingCustomers.data[0].id;
          console.log('Found existing Stripe customer by email:', stripeCustomerId);
        } else {
          // Create a new Stripe customer if none exists
          console.log('Creating new Stripe customer for user...');
          const customer = await stripe.customers.create({
            email: userData.user.email,
            metadata: {
              supabase_user_id: userId,
            },
          });
          stripeCustomerId = customer.id;
          console.log('Created new Stripe customer:', stripeCustomerId);
        }
      } catch (stripeError) {
        console.error('Error handling Stripe customer:', stripeError);
        return NextResponse.json(
          { error: 'Failed to create or find Stripe customer' },
          { status: 500 }
        );
      }
    }

    if (!stripeCustomerId) {
      return NextResponse.json(
        { error: 'No Stripe customer found for user' },
        { status: 404 }
      );
    }

    // Create customer portal session with dynamic return URL
    const { returnUrl } = body;
    const finalReturnUrl = returnUrl || `${process.env.NODE_ENV === 'development'
      ? 'http://localhost:3000'
      : 'https://roukey.online'}/billing`;

    console.log('Creating portal session for customer:', stripeCustomerId);

    const portalSession = await stripe.billingPortal.sessions.create({
      customer: stripeCustomerId,
      return_url: finalReturnUrl,
    });

    console.log('Portal session created successfully:', portalSession.id);

    return NextResponse.json({
      url: portalSession.url
    });

  } catch (error) {
    console.error('Error creating customer portal session:', error);
    
    if (error instanceof Stripe.errors.StripeError) {
      return NextResponse.json(
        { error: `Stripe error: ${error.message}` },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
